

<?php $__env->startSection('content'); ?>



<!-- Modern Two-Column Login Layout -->
<div class="container">
    <!-- Left Column - Login Form -->
    <div class="login-section">
        <!-- Logo/Brand -->
        <img src="<?php echo e(url('img/logo.png')); ?>" alt="WinCash" class="logo">

        <?php if(!$fazersenha): ?>
            <!-- Login Form -->
            <div id="login-content" class="login-form-content">
                <h2>Acesso ao Painel</h2>


                <?php if(session('error')): ?>
                    <div class="alert alert-error">
                        <?php echo e(session('error')); ?>

                    </div>
                <?php endif; ?>

                <?php if(session('success')): ?>
                    <div class="alert alert-success">
                        <?php echo e(session('success')); ?>

                    </div>
                <?php endif; ?>

                <form action="<?php echo e(route('login')); ?>" method="POST" id="form-login" class="login-form" aria-label="Formulário de login">
                    <?php echo e(csrf_field()); ?>


                    <div class="input-field">
                        <label for="email">E-mail</label>
                        <input type="email" name="email" id="email" placeholder="E-mail" required="required" />
                    </div>

                    <div class="input-field">
                        <label for="senha">Senha</label>
                        <div class="password-container">
                            <input type="password" name="senha" id="senha" placeholder="Senha" required="required" />
                            <span class="toggle-password" id="toggle-password">
                                <img src="<?php echo e(url('img/eye-icon.svg')); ?>" alt="Mostrar senha" id="eye-icon">
                            </span>
                        </div>
                    </div>

                    <div class="remember-me">
                        <input type="checkbox" id="manter_logado" name="manter_logado" value="S">
                        <label for="manter_logado">Manter Logado</label>
                    </div>

                    <button type="submit" class="btn-login">
                        ENTRAR
                    </button>
                </form>

                <div class="forgot-password">
                    <button type="button" onclick="$('#login-content').hide(); $('#forgot-content').fadeIn();">
                        Esqueceu a senha?
                    </button>
                </div>
            </div>

            <!-- Forgot Password Form -->
            <div id="forgot-content" class="forgot-password-content" style="display:none">
                <h2>Recuperar Senha</h2>

                <form action="<?php echo e(route('lembrarsenha')); ?>" method="POST" id="form-lembrarsenha" class="login-form" aria-label="Formulário de recuperação de senha">
                    <?php echo e(csrf_field()); ?>


                    <div class="input-field">
                        <label for="email-recovery">E-mail</label>
                        <input type="email" name="email" id="email-recovery" placeholder="Digite seu e-mail" required="required" />
                    </div>

                    <button type="submit" class="btn-login">
                        RECUPERAR SENHA
                    </button>
                </form>

                <div class="back-to-login">
                    <button type="button" onclick="$('#forgot-content').hide(); $('#login-content').fadeIn();">
                        Voltar para o login
                    </button>
                </div>
            </div>
        <?php else: ?>
            <!-- Reset Password Form -->
            <div id="reset-content" class="reset-password-content">
                <h2>Nova Senha</h2>
                
                <div class="user-email">
                    <?php echo e($fazersenha->email); ?>

                </div>

                <form action="<?php echo e(route('lembrarsenha')); ?>" method="POST" id="form-novasenha" class="login-form" aria-label="Formulário de nova senha">
                    <?php echo e(csrf_field()); ?>

                    <input type="hidden" name="token" value="<?php echo e($fazersenha->token); ?>" />
                    <input type="hidden" name="email" value="<?php echo e($fazersenha->email); ?>" />

                    <div class="input-field">
                        <label for="senha">Nova Senha</label>
                        <div class="password-container">
                            <input type="password" name="senha" id="senha" placeholder="Digite sua nova senha" required="required" />
                            <span class="toggle-password" id="toggle-password">
                                <img src="<?php echo e(url('img/eye-icon.svg')); ?>" alt="Mostrar senha" id="eye-icon">
                            </span>
                        </div>
                    </div>

                    <button type="submit" class="btn-login">
                        SALVAR SENHA
                    </button>
                </form>
            </div>
        <?php endif; ?>
    </div>

    <!-- Right Column - Hero Section -->
    <div class="info-section">
        <div class="illustration">
            <img src="<?php echo e(url('img/ilustration.svg')); ?>" alt="Ilustração">
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.auth', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /var/www/app/resources/views/admin/login.blade.php ENDPATH**/ ?>