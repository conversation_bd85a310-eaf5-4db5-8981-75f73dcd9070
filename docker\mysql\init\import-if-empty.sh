#!/bin/bash

# Função para log
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Aguarda o MySQL iniciar completamente (com timeout de 300 segundos)
log "Aguardando MySQL iniciar..."
timeout=300
counter=0
while ! mysqladmin ping -h"localhost" -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" --silent; do
    counter=$((counter + 1))
    if [ "$counter" -ge "$timeout" ]; then
        log "Timeout aguardando MySQL iniciar"
        exit 1
    fi
    sleep 1
done
log "MySQL está pronto"

# Verifica se o banco de dados existe
if ! mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "USE $MYSQL_DATABASE" >/dev/null 2>&1; then
    log "Banco de dados $MYSQL_DATABASE não existe. Criando..."
    mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS $MYSQL_DATABASE"
fi

# Verifica se existem tabelas no banco de dados
log "Verificando tabelas existentes..."
TABLES=$(mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "USE $MYSQL_DATABASE; SHOW TABLES;" | wc -l)
log "Número de tabelas encontradas: $TABLES"

# Verifica se o arquivo de backup existe
if [ -f "/docker-entrypoint-initdb.d/backup.sql" ]; then
    log "Arquivo de backup encontrado"
    
    # Se não houver tabelas, importa o backup
    if [ "$TABLES" -le 1 ]; then
        log "Banco de dados vazio. Iniciando importação do backup..."
        
        # Tenta importar com usuário root primeiro
        if mysql -u"root" -p"$MYSQL_ROOT_PASSWORD" "$MYSQL_DATABASE" < /docker-entrypoint-initdb.d/backup.sql; then
            log "Backup importado com sucesso usando root!"
        else
            log "Falha ao importar com root, tentando com usuário normal..."
            if mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE" < /docker-entrypoint-initdb.d/backup.sql; then
                log "Backup importado com sucesso usando $MYSQL_USER!"
            else
                log "ERRO: Falha ao importar o backup"
                exit 1
            fi
        fi
        
        # Verifica se as tabelas foram realmente criadas
        NEW_TABLES=$(mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "USE $MYSQL_DATABASE; SHOW TABLES;" | wc -l)
        log "Número de tabelas após importação: $NEW_TABLES"
        
        if [ "$NEW_TABLES" -le 1 ]; then
            log "ERRO: Nenhuma tabela foi criada após a importação"
            exit 1
        fi
        
        log "Importação concluída com sucesso!"
    else
        log "Banco de dados já possui tabelas. Nenhuma ação necessária."
    fi
else
    log "ERRO: Arquivo de backup não encontrado em /docker-entrypoint-initdb.d/backup.sql"
    ls -l /docker-entrypoint-initdb.d/
    exit 1
fi 